
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Calendar, MapPin, Users, ArrowRight } from 'lucide-react';
import NewsletterSection from './NewsletterSection';
import VolunteerDonateSection from './VolunteerDonateSection';
import HeroImageSlider from './components/HeroImageSlider';

const Home = () => {
  // Sample featured blogs
  const featuredBlogs = [
    {
      id: 1,
      slug: 'refugee-success-stories-2024',
      image: '/RIA-Recognition-Dinner-Pic-4.jpg',
      title: 'Inspiring Success Stories from Our Community',
      category: 'Success Stories',
      author: 'RIA Team',
      date: '2024-03-15',
      excerpt: 'Meet the incredible individuals who have transformed their lives through our programs and are now giving back to their communities.'
    },
    {
      id: 2,
      slug: 'college-preparation-program-launch',
      image: '/US-College-Life-101-Program-2.jpg',
      title: 'New College Preparation Program Launches',
      category: 'Education',
      author: 'Education Team',
      date: '2024-03-10',
      excerpt: 'Our comprehensive college preparation program is now accepting applications for the spring semester.'
    },
    {
      id: 3,
      slug: 'women-leadership-workshop-recap',
      image: '/IMG_9829 (1).jpg',
      title: 'Women\'s Leadership Workshop: Empowering Change',
      category: 'Empowerment',
      author: 'Leadership Team',
      date: '2024-03-05',
      excerpt: 'Recap of our recent women\'s leadership workshop and the impact it\'s having on our community.'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section with Image Slider */}
      <HeroImageSlider className="text-white py-20 lg:py-32 min-h-screen flex items-center">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Empowering Refugee Families to Build Brighter Futures
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-white/90">
              Through education, community support, and advocacy, we help refugee families integrate and thrive in their new home.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/programs"
                className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-50 transition-colors inline-flex items-center justify-center"
              >
                Explore Our Programs
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <Link
                to="/donate"
                className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors inline-flex items-center justify-center"
              >
                Support Our Mission
              </Link>
            </div>
          </div>
        </div>
      </HeroImageSlider>

      {/* Mission Section */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Mission
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We believe every refugee family deserves the opportunity to thrive in their new community through comprehensive support, education, and empowerment programs.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-8 rounded-2xl bg-blue-50">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Community Integration</h3>
              <p className="text-gray-600">
                Building bridges between refugee families and their new communities through cultural exchange and support networks.
              </p>
            </div>
            
            <div className="text-center p-8 rounded-2xl bg-green-50">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <img src="/icons8-graduation-100.png" alt="Education" className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Education & Training</h3>
              <p className="text-gray-600">
                Providing educational opportunities and job training programs to help families achieve economic independence.
              </p>
            </div>
            
            <div className="text-center p-8 rounded-2xl bg-purple-50">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <img src="/icons8-family-100.png" alt="Family Support" className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Family Support</h3>
              <p className="text-gray-600">
                Comprehensive support services addressing the unique needs of refugee families in their resettlement journey.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Programs */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Featured Programs
            </h2>
            <p className="text-xl text-gray-600">
              Discover our comprehensive programs designed to support every aspect of refugee integration.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
              <img
                src="/US-College-Life-101-Program-2.jpg"
                alt="US College Life 101"
                className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="p-6">
                <span className="inline-block bg-blue-100 text-blue-800 text-sm font-semibold px-3 py-1 rounded-full mb-3">
                  Education
                </span>
                <h3 className="text-xl font-bold text-gray-900 mb-3">US College Life 101</h3>
                <p className="text-gray-600 mb-4">
                  Comprehensive college preparation program helping refugee students navigate higher education.
                </p>
                <Link
                  to="/program/us-college-life-101"
                  className="text-blue-600 font-semibold hover:text-blue-700 inline-flex items-center"
                >
                  Learn More <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>
            
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
              <img
                src="/IMG_9829 (1).jpg"
                alt="Women Empowerment"
                className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="p-6">
                <span className="inline-block bg-purple-100 text-purple-800 text-sm font-semibold px-3 py-1 rounded-full mb-3">
                  Empowerment
                </span>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Women Empowerment & Leadership</h3>
                <p className="text-gray-600 mb-4">
                  Leadership development program for refugee and immigrant women.
                </p>
                <Link
                  to="/program/women-empowerment-leadership"
                  className="text-blue-600 font-semibold hover:text-blue-700 inline-flex items-center"
                >
                  Learn More <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>
            
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
              <img
                src="/IMG_6267-scaled (1).jpg"
                alt="Job Training"
                className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="p-6">
                <span className="inline-block bg-green-100 text-green-800 text-sm font-semibold px-3 py-1 rounded-full mb-3">
                  Employment
                </span>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Job Readiness Training</h3>
                <p className="text-gray-600 mb-4">
                  Comprehensive employment preparation program covering job search and interview skills.
                </p>
                <Link
                  to="/program/job-readiness-training"
                  className="text-blue-600 font-semibold hover:text-blue-700 inline-flex items-center"
                >
                  Learn More <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
          
          <div className="text-center mt-12">
            <Link
              to="/programs"
              className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center"
            >
              View All Programs
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Recent Blog Posts */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Latest Stories
            </h2>
            <p className="text-xl text-gray-600">
              Read about the impact we're making in our community.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredBlogs.map((blog) => (
              <article key={blog.id} className="bg-white rounded-2xl shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
                <img
                  src={blog.image}
                  alt={blog.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="inline-block bg-blue-100 text-blue-800 text-sm font-semibold px-3 py-1 rounded-full">
                      {blog.category}
                    </span>
                    <time className="text-sm text-gray-500">
                      {new Date(blog.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </time>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                    {blog.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {blog.excerpt}
                  </p>
                  <Link
                    to={`/blog/${blog.slug}`}
                    className="text-blue-600 font-semibold hover:text-blue-700 inline-flex items-center"
                  >
                    Read More <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </div>
              </article>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Link
              to="/blogs"
              className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center"
            >
              View All Posts
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Volunteer & Donate Section */}
      <VolunteerDonateSection />

      {/* Newsletter Section */}
      <NewsletterSection />

      {/* Footer */}
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <img
                src="/RIA_Logo_Color_Web.png"
                alt="RIA Logo"
                className="h-12 w-auto"
              />
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/programs" className="hover:text-white transition-colors">Education & Literacy</Link></li>
                <li><Link to="/programs" className="hover:text-white transition-colors">Workforce Development</Link></li>
                <li><Link to="/programs" className="hover:text-white transition-colors">Community Integration</Link></li>
                <li><Link to="/programs" className="hover:text-white transition-colors">Women's Empowerment</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link to="/donate" className="hover:text-white transition-colors">Donate</Link></li>
                <li><Link to="/volunteer" className="hover:text-white transition-colors">Volunteer</Link></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;
