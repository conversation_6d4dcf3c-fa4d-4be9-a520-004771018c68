import React, { useState } from 'react';
import { Heart, DollarSign, Users, Zap, CheckCircle } from 'lucide-react';
import NewsletterSection from './NewsletterSection';

const Donate = () => {
  const [donationAmount, setDonationAmount] = useState('');
  const [donationType, setDonationType] = useState('one-time');
  const [customAmount, setCustomAmount] = useState('');

  const predefinedAmounts = [25, 50, 100, 250, 500, 1000];

  const impactItems = [
    {
      amount: '$25',
      impact: 'Provides English learning materials for one student for a month',
      icon: '📚'
    },
    {
      amount: '$50',
      impact: 'Covers transportation costs for job interview preparation workshops',
      icon: '🚗'
    },
    {
      amount: '$100',
      impact: 'Funds a complete job readiness training session for 5 participants',
      icon: '💼'
    },
    {
      amount: '$250',
      impact: 'Supports a family through our comprehensive integration program for one month',
      icon: '👨‍👩‍👧‍👦'
    }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const amount = customAmount || donationAmount;
    console.log('Donation submitted:', { amount, type: donationType });
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 to-blue-800 text-white py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Support Our Mission
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
              Your generous donation helps refugee families build new lives, gain independence, and contribute to our community.
            </p>
          </div>
        </div>
      </section>

      {/* Impact Section */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Your Impact
            </h2>
            <p className="text-xl text-gray-600">
              See how your donation makes a real difference
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {impactItems.map((item, index) => (
              <div key={index} className="bg-gray-50 rounded-2xl p-8 flex items-start space-x-4">
                <div className="text-4xl">{item.icon}</div>
                <div>
                  <h3 className="text-2xl font-bold text-blue-600 mb-2">{item.amount}</h3>
                  <p className="text-gray-700">{item.impact}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Donation Form */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Make a Donation
            </h2>
            <p className="text-xl text-gray-600">
              Every contribution helps transform lives
            </p>
          </div>
          
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Donation Type */}
              <div>
                <label className="block text-lg font-semibold text-gray-900 mb-4">
                  Donation Type
                </label>
                <div className="grid md:grid-cols-2 gap-4">
                  <label className="flex items-center p-4 border-2 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="donationType"
                      value="one-time"
                      checked={donationType === 'one-time'}
                      onChange={(e) => setDonationType(e.target.value)}
                      className="h-4 w-4 text-blue-600"
                    />
                    <div className="ml-3">
                      <div className="text-lg font-medium text-gray-900">One-time</div>
                      <div className="text-gray-500">Make a single donation</div>
                    </div>
                  </label>
                  
                  <label className="flex items-center p-4 border-2 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                    <input
                      type="radio"
                      name="donationType"
                      value="monthly"
                      checked={donationType === 'monthly'}
                      onChange={(e) => setDonationType(e.target.value)}
                      className="h-4 w-4 text-blue-600"
                    />
                    <div className="ml-3">
                      <div className="text-lg font-medium text-gray-900">Monthly</div>
                      <div className="text-gray-500">Recurring monthly donation</div>
                    </div>
                  </label>
                </div>
              </div>

              {/* Amount Selection */}
              <div>
                <label className="block text-lg font-semibold text-gray-900 mb-4">
                  Donation Amount
                </label>
                <div className="grid grid-cols-3 md:grid-cols-6 gap-3 mb-4">
                  {predefinedAmounts.map((amount) => (
                    <button
                      key={amount}
                      type="button"
                      onClick={() => {
                        setDonationAmount(amount);
                        setCustomAmount('');
                      }}
                      className={`p-3 border-2 rounded-lg font-semibold transition-colors ${
                        donationAmount === amount && !customAmount
                          ? 'border-blue-600 bg-blue-50 text-blue-600'
                          : 'border-gray-300 hover:border-blue-300'
                      }`}
                    >
                      ${amount}
                    </button>
                  ))}
                </div>
                
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <DollarSign className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="number"
                    placeholder="Enter custom amount"
                    value={customAmount}
                    onChange={(e) => {
                      setCustomAmount(e.target.value);
                      setDonationAmount('');
                    }}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Payment Button */}
              <div className="pt-6">
                <button
                  type="submit"
                  disabled={!donationAmount && !customAmount}
                  className="w-full bg-blue-600 text-white py-4 px-6 rounded-lg font-semibold text-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  <Heart className="h-5 w-5 mr-2" />
                  Donate {donationType === 'monthly' ? 'Monthly' : 'Now'}
                  {(donationAmount || customAmount) && (
                    <span className="ml-2">
                      ${customAmount || donationAmount}
                    </span>
                  )}
                </button>
              </div>

              <div className="text-center text-sm text-gray-500">
                <p>🔒 Your donation is secure and processed through our trusted payment partner.</p>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* Other Ways to Give */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Other Ways to Support Us
            </h2>
            <p className="text-xl text-gray-600">
              There are many ways to make a difference
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-8 bg-blue-50 rounded-2xl">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Volunteer Your Time</h3>
              <p className="text-gray-600 mb-6">
                Share your skills and time to directly support refugee families.
              </p>
              <a
                href="/volunteer"
                className="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700"
              >
                Learn More →
              </a>
            </div>
            
            <div className="text-center p-8 bg-green-50 rounded-2xl">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Zap className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Corporate Partnership</h3>
              <p className="text-gray-600 mb-6">
                Partner with us to create lasting impact in your community.
              </p>
              <a
                href="/contact"
                className="inline-flex items-center text-green-600 font-semibold hover:text-green-700"
              >
                Get in Touch →
              </a>
            </div>
            
            <div className="text-center p-8 bg-purple-50 rounded-2xl">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Spread the Word</h3>
              <p className="text-gray-600 mb-6">
                Help us reach more people by sharing our mission with your network.
              </p>
              <div className="space-x-4">
                <button className="text-purple-600 font-semibold hover:text-purple-700">
                  Share →
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <NewsletterSection />

      {/* Footer */}
      <footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="space-y-4">
              <img
                src="/RIA_Logo_Color_Web.png"
                alt="RIA Logo"
                className="h-12 w-auto"
              />
              <p className="text-gray-400">
                Empowering refugee families to build brighter futures.
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Programs</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/programs" className="hover:text-white transition-colors">Education & Literacy</a></li>
                <li><a href="/programs" className="hover:text-white transition-colors">Workforce Development</a></li>
                <li><a href="/programs" className="hover:text-white transition-colors">Community Integration</a></li>
                <li><a href="/programs" className="hover:text-white transition-colors">Women's Empowerment</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Get Involved</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/donate" className="hover:text-white transition-colors">Donate</a></li>
                <li><a href="/volunteer" className="hover:text-white transition-colors">Volunteer</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Advocate</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Corporate Partnerships</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-3 text-gray-400">
                <div>E-mail: <a href="mailto:<EMAIL>" className="hover:text-white transition-colors"><EMAIL></a></div>
                <div className="mt-2 font-semibold text-white">Pastor Belton:</div>
                <div className="mb-2">(319) 491-3486</div>
                <div className="font-semibold text-white">Jean-Paul:</div>
                <div>(319) 383-9819</div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8 text-center text-base text-gray-400">
            &copy; {new Date().getFullYear()} Refugee & Immigration Association. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Donate;
