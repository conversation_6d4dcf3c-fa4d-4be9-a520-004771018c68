import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X } from 'lucide-react';

// shadcn/ui primitives (replace with actual imports if using shadcn/ui)
// import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from 'components/ui/dropdown-menu';

const dropdowns = [
  {
    label: 'About',
    items: [
      { label: 'Who We Are', to: '/about/who-we-are' },
      { label: 'The Board', to: '/about/board' },
    ],
  },
  {
    label: 'What We Do',
    items: [
      { label: 'Programs', to: '/programs' },
      { label: 'Events', to: '/events' },
    ],
  },
  {
    label: 'Resources',
    items: [
      { label: 'General Resources', to: '/resources/general' },
      { label: 'Linn County', to: '/resources/linn-county' },
      { label: 'Johnson County', to: '/resources/johnson-county' },
    ],
  },
];

const standaloneLinks = [
  { label: 'Blogs', to: '/blogs' },
  { label: 'Gallery', to: '/gallery' },
  { label: 'Contact', to: '/contact' },
];

import { useHeroImage } from './contexts/HeroImageContext';

// Mapping of route to background image (for non-home pages)
const navbarBgImages = {
  '/about/who-we-are': '/RIA-Recognition-Dinner-Pic-4.jpg',
  '/about/board': '/RIA-Recognition-Dinner-Pic-4.jpg',
  '/programs': '/RIA-Recognition-Dinner-Pic-4.jpg',
  '/events': '/RIA-Recognition-Dinner-Pic-4.jpg',
  '/resources/general': '/RIA-Recognition-Dinner-Pic-4.jpg',
  '/resources/linn-county': '/RIA-Recognition-Dinner-Pic-4.jpg',
  '/resources/johnson-county': '/RIA-Recognition-Dinner-Pic-4.jpg',
  '/blogs': '/RIA-Recognition-Dinner-Pic-4.jpg',
  '/gallery': '/RIA-Recognition-Dinner-Pic-4.jpg',
  '/contact': '/RIA-Recognition-Dinner-Pic-4.jpg',
};

function getNavbarBgImage(pathname, getCurrentImage) {
  // For home page, use the current hero image
  if (pathname === '/') {
    return getCurrentImage();
  }

  // Try to match the full path, or fallback to the closest parent
  if (navbarBgImages[pathname]) return navbarBgImages[pathname];
  // Try to match by parent route
  const parent = Object.keys(navbarBgImages).find(key => pathname.startsWith(key));
  return navbarBgImages[parent] || '/RIA-Recognition-Dinner-Pic-4.jpg';
}

export default function Navbar({ logoSizeOverride }) {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [openDropdown, setOpenDropdown] = useState(null);
  const location = useLocation();

  // Use hero image context for home page, fallback for other pages
  let getCurrentImage, bgImage;
  try {
    const heroContext = useHeroImage();
    getCurrentImage = heroContext.getCurrentImage;
    bgImage = getNavbarBgImage(location.pathname, getCurrentImage);
  } catch (error) {
    // Fallback for pages that don't have hero image context
    bgImage = getNavbarBgImage(location.pathname, () => '/RIA-Recognition-Dinner-Pic-4.jpg');
  }

  // Mobile dropdown toggle
  const handleDropdownClick = (idx) => {
    setOpenDropdown(openDropdown === idx ? null : idx);
  };

  // Desktop dropdown hover
  const handleDropdownEnter = (idx) => setOpenDropdown(idx);
  const handleDropdownLeave = () => setOpenDropdown(null);

  return (
    <nav className="w-full z-20 fixed top-0 left-0" style={{height: '80px'}}>
      {/* Background image with overlay and blur */}
      <div
        className="absolute inset-0 w-full h-full -z-10"
        style={{
          backgroundImage: `url('${bgImage}')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      />
      <div className="absolute inset-0 w-full h-full -z-10" style={{background: 'rgba(0,0,0,0.7)'}} />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          <Link to="/" className="flex-shrink-0 flex items-center gap-2">
            <img
              className={logoSizeOverride && logoSizeOverride.twClass ? logoSizeOverride.twClass : 'h-12 w-auto'}
              src="/RIA_Logo_Color_Web.svg"
              alt="RIA Logo"
              style={logoSizeOverride ? { width: logoSizeOverride.width || '381.55px', height: logoSizeOverride.height || '112px', maxWidth: logoSizeOverride.maxWidth, objectFit: 'contain' } : {}}
            />
          </Link>
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-2 lg:space-x-6">
            {dropdowns.map((dropdown, idx) => (
              <div
                key={dropdown.label}
                className="relative"
                onMouseEnter={() => handleDropdownEnter(idx)}
                onMouseLeave={handleDropdownLeave}
              >
                <button
                  className={`px-3 py-2 text-sm font-medium flex items-center gap-1 transition-colors rounded focus:outline-none ${openDropdown === idx ? 'text-yellow-400 font-semibold' : 'text-white hover:text-yellow-400'}`}
                  aria-haspopup="true"
                  aria-expanded={openDropdown === idx}
                  type="button"
                >
                  {dropdown.label}
                  <svg className={`w-4 h-4 transition-transform ${openDropdown === idx ? 'rotate-180' : ''} text-white`} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
                </button>
                {/* Dropdown menu */}
                {openDropdown === idx && (
                  <div className="absolute left-0 mt-2 w-52 rounded-md shadow-lg bg-white/95 backdrop-blur ring-1 ring-black ring-opacity-5 z-30 animate-fade-in">
                    {dropdown.items.map((item) => (
                      <Link
                        key={item.to}
                        to={item.to}
                        className={`block px-4 py-3 text-gray-900 hover:bg-yellow-100 hover:text-yellow-700 transition-colors text-base font-medium ${location.pathname === item.to ? 'bg-yellow-50 font-semibold' : ''}`}
                        onClick={() => setOpenDropdown(null)}
                      >
                        {item.label}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
            {standaloneLinks.map((link) => (
              <Link
                key={link.to}
                to={link.to}
                className={`px-3 py-2 text-sm font-medium transition-colors rounded ${location.pathname === link.to ? 'text-yellow-400 font-semibold' : 'text-white hover:text-yellow-400'}`}
              >
                {link.label}
              </Link>
            ))}
          </div>
          {/* Mobile menu button */}
          <div className="md:hidden">
            <button onClick={() => setMobileOpen((v) => !v)} className="text-white hover:text-yellow-400 focus:outline-none">
              {mobileOpen ? <X className="h-7 w-7" /> : <Menu className="h-7 w-7" />}
            </button>
          </div>
        </div>
      </div>
      {/* Mobile Navigation */}
      {mobileOpen && (
        <div className="md:hidden bg-black/90 backdrop-blur border-t border-white/10 shadow-lg animate-fade-in">
          <div className="px-4 py-4 space-y-2">
            {dropdowns.map((dropdown, idx) => (
              <div key={dropdown.label}>
                <button
                  className={`w-full flex justify-between items-center px-3 py-2 text-base font-medium text-white hover:text-yellow-400 focus:outline-none`}
                  onClick={() => handleDropdownClick(idx)}
                  aria-haspopup="true"
                  aria-expanded={openDropdown === idx}
                  type="button"
                >
                  <span>{dropdown.label}</span>
                  <svg className={`w-5 h-5 ml-2 transition-transform ${openDropdown === idx ? 'rotate-180' : ''} text-white`} fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
                </button>
                {openDropdown === idx && (
                  <div className="ml-4 border-l border-yellow-200">
                    {dropdown.items.map((item) => (
                      <Link
                        key={item.to}
                        to={item.to}
                        className={`block px-4 py-2 text-gray-800 hover:bg-yellow-100 hover:text-yellow-700 rounded transition-colors text-base font-medium ${location.pathname === item.to ? 'bg-yellow-50 font-semibold' : ''}`}
                        onClick={() => setMobileOpen(false)}
                      >
                        {item.label}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
            {standaloneLinks.map((link) => (
              <Link
                key={link.to}
                to={link.to}
                className={`block px-3 py-2 text-base font-medium rounded ${location.pathname === link.to ? 'text-yellow-400 font-semibold' : 'text-white hover:text-yellow-400'}`}
                onClick={() => setMobileOpen(false)}
              >
                {link.label}
              </Link>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
} 